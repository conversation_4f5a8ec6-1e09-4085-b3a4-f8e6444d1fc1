</main>

<!-- Enhanced Footer -->
<footer class="relative bg-gradient-to-br from-slate-950 via-slate-900 to-slate-950 border-t border-slate-800/50">
    <!-- Background Pattern -->
    <div class="absolute inset-0 bg-[url('data:image/svg+xml,%3Csvg width="60" height="60" viewBox="0 0 60 60" xmlns="http://www.w3.org/2000/svg"%3E%3Cg fill="none" fill-rule="evenodd"%3E%3Cg fill="%23334155" fill-opacity="0.03"%3E%3Ccircle cx="30" cy="30" r="1"/%3E%3C/g%3E%3C/g%3E%3C/svg%3E')] opacity-20"></div>
    
    <div class="container mx-auto px-4 sm:px-6 lg:px-8 py-16 relative z-10">
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8 lg:gap-12">
            <!-- Brand Section -->
            <div class="lg:col-span-1">
                <a href="{$WEB_ROOT}/index.php" class="text-2xl font-bold text-white flex items-center group mb-6">
                    {if $logo}
                        <img src="{$logo}" alt="{$companyname}" class="h-8 w-auto mr-3">
                    {/if}
                    <span class="bg-gradient-to-r from-white to-gray-200 bg-clip-text text-transparent">
                        {$brand_name|default:$companyname}
                    </span>
                </a>
                <p class="text-gray-400 text-sm leading-relaxed mb-6">
                    {$footer_text|default:"Enterprise-grade hosting solutions with AI-optimized global network infrastructure, delivering unmatched performance and reliability worldwide."}
                </p>
                <div class="flex space-x-4">
                    {if $twitter_url}
                    <a href="{$twitter_url}" target="_blank" rel="noopener noreferrer" class="w-10 h-10 bg-slate-800/50 border border-slate-700/50 rounded-lg flex items-center justify-center text-gray-400 hover:text-white hover:bg-gradient-to-r hover:from-blue-500/20 hover:to-purple-500/20 hover:border-blue-500/30 transition-all duration-300">
                        <i data-lucide="twitter" class="w-4 h-4"></i>
                    </a>
                    {/if}
                    {if $linkedin_url}
                    <a href="{$linkedin_url}" target="_blank" rel="noopener noreferrer" class="w-10 h-10 bg-slate-800/50 border border-slate-700/50 rounded-lg flex items-center justify-center text-gray-400 hover:text-white hover:bg-gradient-to-r hover:from-blue-500/20 hover:to-purple-500/20 hover:border-blue-500/30 transition-all duration-300">
                        <i data-lucide="linkedin" class="w-4 h-4"></i>
                    </a>
                    {/if}
                    {if $github_url}
                    <a href="{$github_url}" target="_blank" rel="noopener noreferrer" class="w-10 h-10 bg-slate-800/50 border border-slate-700/50 rounded-lg flex items-center justify-center text-gray-400 hover:text-white hover:bg-gradient-to-r hover:from-blue-500/20 hover:to-purple-500/20 hover:border-blue-500/30 transition-all duration-300">
                        <i data-lucide="github" class="w-4 h-4"></i>
                    </a>
                    {/if}
                </div>
            </div>
            
            <!-- Services Section -->
            <div>
                <h4 class="font-semibold text-white mb-6 flex items-center">
                    <i data-lucide="server" class="w-4 h-4 mr-2 text-blue-400"></i>
                    Services
                </h4>
                <ul class="space-y-3 text-gray-400">
                    <li><a href="{$WEB_ROOT}/cart.php?gid=1" class="hover:text-white hover:bg-gradient-to-r hover:from-blue-500/10 hover:to-purple-500/10 px-2 py-1 rounded transition-all duration-300 flex items-center"><i data-lucide="share-2" class="w-3 h-3 mr-2"></i>Shared VPS</a></li>
                    <li><a href="{$WEB_ROOT}/cart.php?gid=2" class="hover:text-white hover:bg-gradient-to-r hover:from-blue-500/10 hover:to-purple-500/10 px-2 py-1 rounded transition-all duration-300 flex items-center"><i data-lucide="video" class="w-3 h-3 mr-2"></i>Streaming VPS</a></li>
                    <li><a href="{$WEB_ROOT}/cart.php?gid=3" class="hover:text-white hover:bg-gradient-to-r hover:from-blue-500/10 hover:to-purple-500/10 px-2 py-1 rounded transition-all duration-300 flex items-center"><i data-lucide="database" class="w-3 h-3 mr-2"></i>Dedicated Servers</a></li>
                    <li><a href="{$WEB_ROOT}/cart.php?gid=4" class="hover:text-white hover:bg-gradient-to-r hover:from-blue-500/10 hover:to-purple-500/10 px-2 py-1 rounded transition-all duration-300 flex items-center"><i data-lucide="gamepad-2" class="w-3 h-3 mr-2"></i>Game Hosting</a></li>
                </ul>
            </div>
            
            <!-- Company Section -->
            <div>
                <h4 class="font-semibold text-white mb-6 flex items-center">
                    <i data-lucide="building" class="w-4 h-4 mr-2 text-purple-400"></i>
                    Company
                </h4>
                <ul class="space-y-3 text-gray-400">
                    <li><a href="{$WEB_ROOT}/about.php" class="hover:text-white hover:bg-gradient-to-r hover:from-blue-500/10 hover:to-purple-500/10 px-2 py-1 rounded transition-all duration-300 flex items-center"><i data-lucide="users" class="w-3 h-3 mr-2"></i>About Us</a></li>
                    <li><a href="{$WEB_ROOT}/network.php" class="hover:text-white hover:bg-gradient-to-r hover:from-blue-500/10 hover:to-purple-500/10 px-2 py-1 rounded transition-all duration-300 flex items-center"><i data-lucide="network" class="w-3 h-3 mr-2"></i>Network</a></li>
                    <li><a href="{$WEB_ROOT}/affiliates.php" class="hover:text-white hover:bg-gradient-to-r hover:from-blue-500/10 hover:to-purple-500/10 px-2 py-1 rounded transition-all duration-300 flex items-center"><i data-lucide="users-2" class="w-3 h-3 mr-2"></i>Affiliates</a></li>
                    <li><a href="{$WEB_ROOT}/contact.php" class="hover:text-white hover:bg-gradient-to-r hover:from-blue-500/10 hover:to-purple-500/10 px-2 py-1 rounded transition-all duration-300 flex items-center"><i data-lucide="mail" class="w-3 h-3 mr-2"></i>Contact</a></li>
                </ul>
            </div>
            
            <!-- Support Section -->
            <div>
                <h4 class="font-semibold text-white mb-6 flex items-center">
                    <i data-lucide="life-buoy" class="w-4 h-4 mr-2 text-green-400"></i>
                    Support
                </h4>
                <ul class="space-y-3 text-gray-400">
                    <li><a href="{$WEB_ROOT}/knowledgebase.php" class="hover:text-white hover:bg-gradient-to-r hover:from-blue-500/10 hover:to-purple-500/10 px-2 py-1 rounded transition-all duration-300 flex items-center"><i data-lucide="book-open" class="w-3 h-3 mr-2"></i>Knowledge Base</a></li>
                    <li><a href="{$WEB_ROOT}/submitticket.php" class="hover:text-white hover:bg-gradient-to-r hover:from-blue-500/10 hover:to-purple-500/10 px-2 py-1 rounded transition-all duration-300 flex items-center"><i data-lucide="ticket" class="w-3 h-3 mr-2"></i>Support Tickets</a></li>
                    <li><a href="{$WEB_ROOT}/serverstatus.php" class="hover:text-white hover:bg-gradient-to-r hover:from-blue-500/10 hover:to-purple-500/10 px-2 py-1 rounded transition-all duration-300 flex items-center"><i data-lucide="activity" class="w-3 h-3 mr-2"></i>System Status</a></li>
                    <li><a href="{$WEB_ROOT}/downloads.php" class="hover:text-white hover:bg-gradient-to-r hover:from-blue-500/10 hover:to-purple-500/10 px-2 py-1 rounded transition-all duration-300 flex items-center"><i data-lucide="download" class="w-3 h-3 mr-2"></i>Downloads</a></li>
                </ul>
            </div>
        </div>
        
        <!-- Bottom Section -->
        <div class="mt-16 pt-8 border-t border-slate-800/50">
            <div class="flex flex-col lg:flex-row justify-between items-center space-y-4 lg:space-y-0">
                <div class="flex flex-col sm:flex-row items-center space-y-2 sm:space-y-0 sm:space-x-6 text-sm text-gray-500">
                    <p>&copy; {$date_year} {$companyname}. All rights reserved.</p>
                    <div class="flex space-x-6">
                        <a href="{$WEB_ROOT}/privacy.php" class="hover:text-gray-300 transition-colors duration-300">Privacy Policy</a>
                        <a href="{$WEB_ROOT}/terms.php" class="hover:text-gray-300 transition-colors duration-300">Terms of Service</a>
                        <a href="{$WEB_ROOT}/legal.php" class="hover:text-gray-300 transition-colors duration-300">Legal</a>
                    </div>
                </div>
                <div class="flex items-center space-x-4 text-sm text-gray-500">
                    {if $show_system_status}
                    <div class="flex items-center">
                        <div class="w-2 h-2 bg-green-400 rounded-full mr-2 animate-pulse"></div>
                        <span>All Systems Operational</span>
                    </div>
                    {/if}
                    {if $support_email}
                    <div class="flex items-center">
                        <i data-lucide="mail" class="w-3 h-3 mr-1"></i>
                        <a href="mailto:{$support_email}" class="hover:text-gray-300 transition-colors duration-300">{$support_email}</a>
                    </div>
                    {/if}
                </div>
            </div>
        </div>
    </div>
</footer>

<!-- JavaScript -->
<script src="{$WEB_ROOT}/templates/{$template}/js/theme.js?v={$versionHash}"></script>

<!-- Mobile Menu JavaScript -->
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Mobile menu toggle
    const mobileMenuButton = document.getElementById('mobile-menu-button');
    const mobileMenu = document.getElementById('mobile-menu');
    const hamburgerLines = document.querySelectorAll('.hamburger-line');

    if (mobileMenuButton && mobileMenu) {
        let isOpen = false;
        
        mobileMenuButton.addEventListener('click', () => {
            isOpen = !isOpen;
            mobileMenu.classList.toggle('hidden');
            
            // Animate hamburger to X
            if (isOpen) {
                hamburgerLines[0].style.transform = 'rotate(45deg) translate(6px, 6px)';
                hamburgerLines[1].style.opacity = '0';
                hamburgerLines[2].style.transform = 'rotate(-45deg) translate(6px, -6px)';
            } else {
                hamburgerLines[0].style.transform = 'rotate(0) translate(0, 0)';
                hamburgerLines[1].style.opacity = '1';
                hamburgerLines[2].style.transform = 'rotate(0) translate(0, 0)';
            }
        });
        
        // Close mobile menu when clicking outside
        document.addEventListener('click', (e) => {
            if (!mobileMenuButton.contains(e.target) && !mobileMenu.contains(e.target) && !mobileMenu.classList.contains('hidden')) {
                isOpen = false;
                mobileMenu.classList.add('hidden');
                hamburgerLines[0].style.transform = 'rotate(0) translate(0, 0)';
                hamburgerLines[1].style.opacity = '1';
                hamburgerLines[2].style.transform = 'rotate(0) translate(0, 0)';
            }
        });
    }

    // Mobile Services dropdown toggle
    const mobileServicesToggle = document.getElementById('mobile-services-toggle');
    const mobileServicesSubmenu = document.querySelector('.mobile-services-submenu');
    const mobileServicesChevron = document.getElementById('mobile-services-chevron');

    if (mobileServicesToggle && mobileServicesSubmenu && mobileServicesChevron) {
        mobileServicesToggle.addEventListener('click', () => {
            mobileServicesSubmenu.classList.toggle('hidden');
            mobileServicesChevron.classList.toggle('rotate-180');
        });
    }

    // Initialize Lucide icons
    if (typeof lucide !== 'undefined') {
        lucide.createIcons();
    }
    
    // Smooth scrolling for anchor links
    document.querySelectorAll('a[href^="#"]').forEach(anchor => {
        anchor.addEventListener('click', function (e) {
            e.preventDefault();
            const target = document.querySelector(this.getAttribute('href'));
            if (target) {
                target.scrollIntoView({
                    behavior: 'smooth',
                    block: 'start'
                });
            }
        });
    });
    
    // Add loading states to forms
    document.querySelectorAll('form').forEach(form => {
        form.addEventListener('submit', function() {
            const submitBtn = form.querySelector('button[type="submit"], input[type="submit"]');
            if (submitBtn) {
                submitBtn.disabled = true;
                const originalText = submitBtn.textContent || submitBtn.value;
                submitBtn.textContent = 'Processing...';
                submitBtn.value = 'Processing...';
                
                // Re-enable after 10 seconds as fallback
                setTimeout(() => {
                    submitBtn.disabled = false;
                    submitBtn.textContent = originalText;
                    submitBtn.value = originalText;
                }, 10000);
            }
        });
    });
    
    // Enhanced table responsiveness
    document.querySelectorAll('.table').forEach(table => {
        if (!table.closest('.table-responsive')) {
            const wrapper = document.createElement('div');
            wrapper.className = 'table-responsive';
            table.parentNode.insertBefore(wrapper, table);
            wrapper.appendChild(table);
        }
    });
    
    // Auto-hide alerts after 5 seconds
    document.querySelectorAll('.alert').forEach(alert => {
        if (!alert.classList.contains('alert-permanent')) {
            setTimeout(() => {
                alert.style.opacity = '0';
                alert.style.transform = 'translateY(-20px)';
                setTimeout(() => {
                    alert.remove();
                }, 300);
            }, 5000);
        }
    });
});
</script>

{$footeroutput}

</body>
</html>
