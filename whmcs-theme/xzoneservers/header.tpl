<!DOCTYPE html>
<html lang="{$language}" dir="{$dir}">
<head>
    <meta charset="utf-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <title>{if $kbarticle.title}{$kbarticle.title} - {/if}{$pagetitle} - {$companyname}</title>
    
    <!-- SEO Meta Tags -->
    <meta name="description" content="{if $tagline}{$tagline}{else}Enterprise-grade hosting solutions with AI-optimized global network infrastructure{/if}">
    <meta name="keywords" content="hosting, dedicated servers, VPS, cloud hosting, enterprise hosting">
    <meta name="author" content="{$companyname}">
    <meta name="robots" content="index, follow">
    <meta name="theme-color" content="#0ea5e9">
    
    <!-- Open Graph / Facebook -->
    <meta property="og:type" content="website">
    <meta property="og:url" content="{$systemurl}">
    <meta property="og:title" content="{$pagetitle} - {$companyname}">
    <meta property="og:description" content="{if $tagline}{$tagline}{else}Enterprise-grade hosting solutions{/if}">
    <meta property="og:site_name" content="{$companyname}">
    
    <!-- Twitter -->
    <meta property="twitter:card" content="summary_large_image">
    <meta property="twitter:url" content="{$systemurl}">
    <meta property="twitter:title" content="{$pagetitle} - {$companyname}">
    <meta property="twitter:description" content="{if $tagline}{$tagline}{else}Enterprise-grade hosting solutions{/if}">
    
    <!-- Favicon -->
    <link rel="icon" type="image/png" href="{$WEB_ROOT}/templates/{$template}/images/favicon.png">
    <link rel="apple-touch-icon" href="{$WEB_ROOT}/templates/{$template}/images/apple-touch-icon.png">
    
    <!-- Fonts -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700;900&display=swap" rel="stylesheet">
    
    <!-- CSS Files -->
    <link href="{$WEB_ROOT}/templates/{$template}/css/styles.css?v={$versionHash}" rel="stylesheet">
    <link href="{$WEB_ROOT}/templates/{$template}/css/responsive.css?v={$versionHash}" rel="stylesheet">
    {if $templatefile eq "homepage"}
    <link href="{$WEB_ROOT}/templates/{$template}/css/homepage.css?v={$versionHash}" rel="stylesheet">
    {/if}
    
    <!-- JavaScript Libraries -->
    <script src="https://unpkg.com/lucide@latest/dist/umd/lucide.js"></script>
    
    <!-- Custom CSS Variables -->
    <style>
        :root {
            --xz-primary: {$primary_color|default:'#0ea5e9'};
            --xz-secondary: {$secondary_color|default:'#38bdf8'};
            --xz-accent: {$accent_color|default:'#a855f7'};
            --xz-company-name: '{$companyname}';
        }
    </style>
    
    {$headoutput}
</head>

<body class="{$templatefile} {if $loggedin}logged-in{else}logged-out{/if}" data-phone-cc-input="{$phoneNumberInputStyle}">

{$headeroutput}

<!-- Skip Link for Accessibility -->
<a href="#main-content" class="skip-link">Skip to main content</a>

<!-- Header -->
<header class="fixed top-0 left-0 right-0 z-50 bg-gradient-to-r from-slate-950/95 via-slate-900/95 to-slate-950/95 backdrop-blur-xl border-b border-slate-800/50">
    <div class="container mx-auto px-4 sm:px-6 lg:px-8">
        <div class="flex items-center justify-between h-20">
            <!-- Logo -->
            <a href="{$WEB_ROOT}/index.php" class="text-2xl font-bold text-white flex items-center group">
                {if $logo}
                    <img src="{$logo}" alt="{$companyname}" class="h-8 w-auto mr-3">
                {/if}
                <span class="bg-gradient-to-r from-white to-gray-200 bg-clip-text text-transparent">
                    {$brand_name|default:$companyname}
                </span>
            </a>

            <!-- Desktop Navigation -->
            <nav class="hidden xl:flex items-center space-x-1">
                <div class="relative group">
                    <button class="group relative px-4 py-2 text-gray-300 hover:text-white transition-all duration-300 nav-link flex items-center">
                        <span class="relative z-10 flex items-center">
                            <i data-lucide="server" class="w-4 h-4 mr-2"></i>
                            Services
                            <i data-lucide="chevron-down" class="w-3 h-3 ml-1 transition-transform duration-300 group-hover:rotate-180"></i>
                        </span>
                        <div class="absolute inset-0 bg-gradient-to-r from-blue-500/10 to-purple-500/10 rounded-lg opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
                    </button>
                    <!-- Services Dropdown -->
                    <div class="absolute top-full left-0 mt-2 w-48 bg-slate-900/95 backdrop-blur-xl border border-slate-700/50 rounded-xl shadow-xl opacity-0 invisible group-hover:opacity-100 group-hover:visible transition-all duration-300 z-50">
                        <div class="p-2">
                            <a href="{$WEB_ROOT}/cart.php?gid=1" class="flex items-center px-3 py-2 text-gray-300 hover:text-white hover:bg-gradient-to-r hover:from-blue-500/10 hover:to-purple-500/10 rounded-lg transition-all duration-300">
                                <i data-lucide="share-2" class="w-4 h-4 mr-3"></i>
                                Shared VPS
                            </a>
                            <a href="{$WEB_ROOT}/cart.php?gid=2" class="flex items-center px-3 py-2 text-gray-300 hover:text-white hover:bg-gradient-to-r hover:from-purple-500/10 hover:to-pink-500/10 rounded-lg transition-all duration-300">
                                <i data-lucide="video" class="w-4 h-4 mr-3"></i>
                                Streaming VPS
                            </a>
                            <a href="{$WEB_ROOT}/cart.php?gid=3" class="flex items-center px-3 py-2 text-gray-300 hover:text-white hover:bg-gradient-to-r hover:from-orange-500/10 hover:to-red-500/10 rounded-lg transition-all duration-300">
                                <i data-lucide="database" class="w-4 h-4 mr-3"></i>
                                Dedicated Servers
                            </a>
                        </div>
                    </div>
                </div>
                
                <a href="{$WEB_ROOT}/knowledgebase.php" class="group relative px-4 py-2 text-gray-300 hover:text-white transition-all duration-300 nav-link">
                    <span class="relative z-10 flex items-center">
                        <i data-lucide="book-open" class="w-4 h-4 mr-2"></i>
                        Knowledge Base
                    </span>
                    <div class="absolute inset-0 bg-gradient-to-r from-green-500/10 to-blue-500/10 rounded-lg opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
                </a>
                
                <a href="{$WEB_ROOT}/submitticket.php" class="group relative px-4 py-2 text-gray-300 hover:text-white transition-all duration-300 nav-link">
                    <span class="relative z-10 flex items-center">
                        <i data-lucide="life-buoy" class="w-4 h-4 mr-2"></i>
                        Support
                    </span>
                    <div class="absolute inset-0 bg-gradient-to-r from-pink-500/10 to-orange-500/10 rounded-lg opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
                </a>
                
                <a href="{$WEB_ROOT}/contact.php" class="group relative px-4 py-2 text-gray-300 hover:text-white transition-all duration-300 nav-link">
                    <span class="relative z-10 flex items-center">
                        <i data-lucide="phone" class="w-4 h-4 mr-2"></i>
                        Contact
                    </span>
                    <div class="absolute inset-0 bg-gradient-to-r from-teal-500/10 to-cyan-500/10 rounded-lg opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
                </a>
            </nav>

            <!-- User Menu / Login -->
            <div class="hidden xl:flex items-center space-x-4">
                {if $loggedin}
                    <div class="relative group">
                        <button class="flex items-center px-4 py-2 bg-gradient-to-r from-blue-500/20 to-purple-500/20 border border-blue-500/30 text-white font-semibold rounded-xl hover:from-blue-500/30 hover:to-purple-500/30 transition-all duration-300">
                            <i data-lucide="user" class="w-4 h-4 mr-2"></i>
                            {$clientsdetails.firstname} {$clientsdetails.lastname}
                            <i data-lucide="chevron-down" class="w-3 h-3 ml-2"></i>
                        </button>
                        <!-- User Dropdown -->
                        <div class="absolute top-full right-0 mt-2 w-48 bg-slate-900/95 backdrop-blur-xl border border-slate-700/50 rounded-xl shadow-xl opacity-0 invisible group-hover:opacity-100 group-hover:visible transition-all duration-300 z-50">
                            <div class="p-2">
                                <a href="{$WEB_ROOT}/clientarea.php" class="flex items-center px-3 py-2 text-gray-300 hover:text-white hover:bg-gradient-to-r hover:from-blue-500/10 hover:to-purple-500/10 rounded-lg transition-all duration-300">
                                    <i data-lucide="layout-dashboard" class="w-4 h-4 mr-3"></i>
                                    Dashboard
                                </a>
                                <a href="{$WEB_ROOT}/clientarea.php?action=services" class="flex items-center px-3 py-2 text-gray-300 hover:text-white hover:bg-gradient-to-r hover:from-blue-500/10 hover:to-purple-500/10 rounded-lg transition-all duration-300">
                                    <i data-lucide="server" class="w-4 h-4 mr-3"></i>
                                    My Services
                                </a>
                                <a href="{$WEB_ROOT}/clientarea.php?action=invoices" class="flex items-center px-3 py-2 text-gray-300 hover:text-white hover:bg-gradient-to-r hover:from-blue-500/10 hover:to-purple-500/10 rounded-lg transition-all duration-300">
                                    <i data-lucide="file-text" class="w-4 h-4 mr-3"></i>
                                    Invoices
                                </a>
                                <a href="{$WEB_ROOT}/supporttickets.php" class="flex items-center px-3 py-2 text-gray-300 hover:text-white hover:bg-gradient-to-r hover:from-blue-500/10 hover:to-purple-500/10 rounded-lg transition-all duration-300">
                                    <i data-lucide="ticket" class="w-4 h-4 mr-3"></i>
                                    Support Tickets
                                </a>
                                <div class="border-t border-slate-700/50 my-2"></div>
                                <a href="{$WEB_ROOT}/logout.php" class="flex items-center px-3 py-2 text-red-400 hover:text-red-300 hover:bg-red-500/10 rounded-lg transition-all duration-300">
                                    <i data-lucide="log-out" class="w-4 h-4 mr-3"></i>
                                    Logout
                                </a>
                            </div>
                        </div>
                    </div>
                {else}
                    <a href="{$WEB_ROOT}/login.php" class="px-4 py-2 text-gray-300 hover:text-white transition-all duration-300">
                        <i data-lucide="log-in" class="w-4 h-4 mr-2 inline"></i>
                        Login
                    </a>
                    <a href="{$WEB_ROOT}/register.php" class="inline-flex items-center px-6 py-2.5 bg-gradient-to-r from-blue-500 via-purple-500 to-pink-500 text-white font-semibold rounded-xl hover:shadow-lg hover:shadow-blue-500/25 transition-all duration-300 transform hover:scale-105">
                        <i data-lucide="user-plus" class="w-4 h-4 mr-2"></i>
                        Register
                    </a>
                {/if}
            </div>

            <!-- Mobile Menu Button -->
            <button id="mobile-menu-button" class="xl:hidden p-2 text-white hover:bg-slate-800/50 rounded-lg transition-colors duration-300 relative">
                <div class="w-6 h-6 flex flex-col justify-center items-center">
                    <span class="hamburger-line block w-5 h-0.5 bg-white transition-all duration-300 ease-in-out"></span>
                    <span class="hamburger-line block w-5 h-0.5 bg-white mt-1 transition-all duration-300 ease-in-out"></span>
                    <span class="hamburger-line block w-5 h-0.5 bg-white mt-1 transition-all duration-300 ease-in-out"></span>
                </div>
            </button>
        </div>
    </div>

    <!-- Mobile Menu -->
    <div id="mobile-menu" class="hidden xl:hidden bg-gradient-to-br from-slate-950/98 to-slate-900/98 backdrop-blur-xl border-t border-slate-800/50">
        <div class="px-4 py-6 space-y-3">
            <!-- Mobile Services -->
            <div class="services-mobile-dropdown">
                <button class="flex items-center justify-between w-full px-4 py-3 text-gray-300 hover:text-white hover:bg-gradient-to-r hover:from-blue-500/10 hover:to-purple-500/10 rounded-xl transition-all duration-300" id="mobile-services-toggle">
                    <div class="flex items-center">
                        <i data-lucide="server" class="w-5 h-5 mr-3"></i>
                        Services
                    </div>
                    <i data-lucide="chevron-down" class="w-4 h-4 transition-transform duration-300" id="mobile-services-chevron"></i>
                </button>
                <div class="mobile-services-submenu hidden mt-2 ml-4 space-y-2">
                    <a href="{$WEB_ROOT}/cart.php?gid=1" class="flex items-center px-4 py-2 text-gray-400 hover:text-white hover:bg-gradient-to-r hover:from-blue-500/10 hover:to-purple-500/10 rounded-lg transition-all duration-300">
                        <i data-lucide="share-2" class="w-4 h-4 mr-3"></i>
                        Shared VPS
                    </a>
                    <a href="{$WEB_ROOT}/cart.php?gid=2" class="flex items-center px-4 py-2 text-gray-400 hover:text-white hover:bg-gradient-to-r hover:from-purple-500/10 hover:to-pink-500/10 rounded-lg transition-all duration-300">
                        <i data-lucide="video" class="w-4 h-4 mr-3"></i>
                        Streaming VPS
                    </a>
                    <a href="{$WEB_ROOT}/cart.php?gid=3" class="flex items-center px-4 py-2 text-gray-400 hover:text-white hover:bg-gradient-to-r hover:from-orange-500/10 hover:to-red-500/10 rounded-lg transition-all duration-300">
                        <i data-lucide="database" class="w-4 h-4 mr-3"></i>
                        Dedicated Servers
                    </a>
                </div>
            </div>
            
            <a href="{$WEB_ROOT}/knowledgebase.php" class="flex items-center px-4 py-3 text-gray-300 hover:text-white hover:bg-gradient-to-r hover:from-green-500/10 hover:to-blue-500/10 rounded-xl transition-all duration-300">
                <i data-lucide="book-open" class="w-5 h-5 mr-3"></i>
                Knowledge Base
            </a>
            
            <a href="{$WEB_ROOT}/submitticket.php" class="flex items-center px-4 py-3 text-gray-300 hover:text-white hover:bg-gradient-to-r hover:from-pink-500/10 hover:to-orange-500/10 rounded-xl transition-all duration-300">
                <i data-lucide="life-buoy" class="w-5 h-5 mr-3"></i>
                Support
            </a>
            
            <a href="{$WEB_ROOT}/contact.php" class="flex items-center px-4 py-3 text-gray-300 hover:text-white hover:bg-gradient-to-r hover:from-teal-500/10 hover:to-cyan-500/10 rounded-xl transition-all duration-300">
                <i data-lucide="phone" class="w-5 h-5 mr-3"></i>
                Contact
            </a>
            
            {if $loggedin}
                <div class="pt-4 border-t border-slate-800/50">
                    <div class="text-gray-400 text-sm mb-3 px-4">Account</div>
                    <a href="{$WEB_ROOT}/clientarea.php" class="flex items-center px-4 py-3 text-gray-300 hover:text-white hover:bg-gradient-to-r hover:from-blue-500/10 hover:to-purple-500/10 rounded-xl transition-all duration-300">
                        <i data-lucide="layout-dashboard" class="w-5 h-5 mr-3"></i>
                        Dashboard
                    </a>
                    <a href="{$WEB_ROOT}/logout.php" class="flex items-center px-4 py-3 text-red-400 hover:text-red-300 hover:bg-red-500/10 rounded-xl transition-all duration-300">
                        <i data-lucide="log-out" class="w-5 h-5 mr-3"></i>
                        Logout
                    </a>
                </div>
            {else}
                <div class="pt-4 border-t border-slate-800/50 space-y-3">
                    <a href="{$WEB_ROOT}/login.php" class="flex items-center justify-center w-full px-6 py-3 bg-gradient-to-r from-slate-700 to-slate-600 text-white font-semibold rounded-xl hover:from-slate-600 hover:to-slate-500 transition-all duration-300">
                        <i data-lucide="log-in" class="w-4 h-4 mr-2"></i>
                        Login
                    </a>
                    <a href="{$WEB_ROOT}/register.php" class="flex items-center justify-center w-full px-6 py-3 bg-gradient-to-r from-blue-500 via-purple-500 to-pink-500 text-white font-semibold rounded-xl hover:shadow-lg hover:shadow-blue-500/25 transition-all duration-300">
                        <i data-lucide="user-plus" class="w-4 h-4 mr-2"></i>
                        Register
                    </a>
                </div>
            {/if}
        </div>
    </div>
</header>

<!-- Main Content Wrapper -->
<main id="main-content" class="pt-20 min-h-screen bg-gradient-to-br from-slate-950 via-slate-900 to-slate-950">
    {if $templatefile eq "homepage"}
        <div class="hero-gradient absolute inset-0 pointer-events-none"></div>
    {/if}
