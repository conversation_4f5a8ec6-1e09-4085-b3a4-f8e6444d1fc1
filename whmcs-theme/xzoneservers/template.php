<?php
/**
 * X-ZoneServers WHMCS Theme Configuration
 * 
 * This file contains the main theme configuration and template variables
 * for the X-ZoneServers WHMCS theme.
 */

// Theme Information
$templatename = "X-ZoneServers";
$version = "1.0.0";
$author = "X-ZoneServers Team";
$description = "Custom WHMCS theme matching X-ZoneServers website design";

// Template Configuration
if (!defined("WHMCS")) {
    die("This file cannot be accessed directly");
}

// Add custom CSS and JS files
$template_config = array(
    'name' => $templatename,
    'version' => $version,
    'author' => $author,
    'description' => $description,
    'parent' => 'six', // Inherit from Six theme
    'css' => array(
        'css/styles.css',
        'css/responsive.css',
        'css/animations.css'
    ),
    'js' => array(
        'js/theme.js',
        'js/animations.js'
    )
);

// Custom template variables
function xzoneservers_config() {
    return array(
        // Brand Settings
        'brand_name' => array(
            'FriendlyName' => 'Brand Name',
            'Type' => 'text',
            'Size' => '50',
            'Default' => 'X-ZoneServers',
            'Description' => 'Your company brand name'
        ),
        'brand_tagline' => array(
            'FriendlyName' => 'Brand Tagline',
            'Type' => 'text',
            'Size' => '100',
            'Default' => 'Enterprise-Grade Hosting Solutions Worldwide',
            'Description' => 'Your company tagline or slogan'
        ),
        
        // Color Customization
        'primary_color' => array(
            'FriendlyName' => 'Primary Color',
            'Type' => 'text',
            'Size' => '10',
            'Default' => '#0ea5e9',
            'Description' => 'Primary brand color (hex code)'
        ),
        'secondary_color' => array(
            'FriendlyName' => 'Secondary Color',
            'Type' => 'text',
            'Size' => '10',
            'Default' => '#38bdf8',
            'Description' => 'Secondary brand color (hex code)'
        ),
        'accent_color' => array(
            'FriendlyName' => 'Accent Color',
            'Type' => 'text',
            'Size' => '10',
            'Default' => '#a855f7',
            'Description' => 'Accent color for highlights (hex code)'
        ),
        
        // Layout Options
        'enable_animations' => array(
            'FriendlyName' => 'Enable Animations',
            'Type' => 'yesno',
            'Default' => 'on',
            'Description' => 'Enable smooth animations and transitions'
        ),
        'enable_gradients' => array(
            'FriendlyName' => 'Enable Gradients',
            'Type' => 'yesno',
            'Default' => 'on',
            'Description' => 'Enable gradient backgrounds and effects'
        ),
        'enable_glassmorphism' => array(
            'FriendlyName' => 'Enable Glassmorphism',
            'Type' => 'yesno',
            'Default' => 'on',
            'Description' => 'Enable glassmorphism card effects'
        ),
        
        // Social Media Links
        'twitter_url' => array(
            'FriendlyName' => 'Twitter URL',
            'Type' => 'text',
            'Size' => '100',
            'Default' => 'https://twitter.com/xzoneservers',
            'Description' => 'Your Twitter profile URL'
        ),
        'linkedin_url' => array(
            'FriendlyName' => 'LinkedIn URL',
            'Type' => 'text',
            'Size' => '100',
            'Default' => 'https://linkedin.com/company/xzoneservers',
            'Description' => 'Your LinkedIn profile URL'
        ),
        'github_url' => array(
            'FriendlyName' => 'GitHub URL',
            'Type' => 'text',
            'Size' => '100',
            'Default' => 'https://github.com/xzoneservers',
            'Description' => 'Your GitHub profile URL'
        ),
        
        // Contact Information
        'support_email' => array(
            'FriendlyName' => 'Support Email',
            'Type' => 'text',
            'Size' => '50',
            'Default' => '<EMAIL>',
            'Description' => 'Primary support email address'
        ),
        'support_phone' => array(
            'FriendlyName' => 'Support Phone',
            'Type' => 'text',
            'Size' => '20',
            'Default' => '******-XZONE-1',
            'Description' => 'Primary support phone number'
        ),
        
        // Footer Settings
        'footer_text' => array(
            'FriendlyName' => 'Footer Text',
            'Type' => 'textarea',
            'Rows' => '3',
            'Default' => 'Enterprise-grade hosting solutions with AI-optimized global network infrastructure, delivering unmatched performance and reliability worldwide.',
            'Description' => 'Footer description text'
        ),
        'show_system_status' => array(
            'FriendlyName' => 'Show System Status',
            'Type' => 'yesno',
            'Default' => 'on',
            'Description' => 'Show system status indicator in footer'
        )
    );
}

// Hook to add custom CSS variables
add_hook('ClientAreaHeadOutput', 1, function($vars) {
    $primary_color = $vars['primary_color'] ?? '#0ea5e9';
    $secondary_color = $vars['secondary_color'] ?? '#38bdf8';
    $accent_color = $vars['accent_color'] ?? '#a855f7';
    
    return "
    <style>
        :root {
            --xz-primary: {$primary_color};
            --xz-secondary: {$secondary_color};
            --xz-accent: {$accent_color};
            --xz-bg-primary: #020617;
            --xz-bg-secondary: #0f172a;
            --xz-text-primary: #d1d5db;
            --xz-text-secondary: #9ca3af;
            --xz-border: #334155;
        }
    </style>
    ";
});

// Custom functions for the theme
function xzoneservers_get_brand_name($vars) {
    return $vars['brand_name'] ?? 'X-ZoneServers';
}

function xzoneservers_get_brand_tagline($vars) {
    return $vars['brand_tagline'] ?? 'Enterprise-Grade Hosting Solutions Worldwide';
}

function xzoneservers_format_price($amount, $currency) {
    return $currency['prefix'] . number_format($amount, 2) . $currency['suffix'];
}

function xzoneservers_get_status_class($status) {
    $status_classes = array(
        'Active' => 'success',
        'Suspended' => 'warning',
        'Terminated' => 'danger',
        'Pending' => 'info',
        'Cancelled' => 'secondary'
    );
    
    return $status_classes[$status] ?? 'secondary';
}
?>
